/**
 * Modal simple et direct pour l'authentification Premium OpenRouter
 * Solution simplifiée pour résoudre les problèmes de modal bloqué
 */

import React, { useState, useEffect } from 'react';
import { premiumAuthService } from '../services/premiumAuthService';
import type { AuthenticationState } from '../types';

interface SimplePremiumModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export const SimplePremiumModal: React.FC<SimplePremiumModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const [apiKey, setApiKey] = useState('');
  const [showApiKey, setShowApiKey] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [authState, setAuthState] = useState<AuthenticationState>({
    user: { isAuthenticated: false, plan: 'free' },
    isLoading: false,
    error: null
  });

  // Écouter les changements d'authentification
  useEffect(() => {
    const unsubscribe = premiumAuthService.onAuthChange((auth) => {
      setAuthState(auth);
      setIsLoading(auth.isLoading);
      setError(auth.error || '');
      
      // Si l'authentification réussit, fermer le modal
      if (auth.user.isAuthenticated && !auth.isLoading) {
        onSuccess?.();
        onClose();
      }
    });

    return unsubscribe;
  }, [onClose, onSuccess]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!apiKey.trim()) {
      setError('Veuillez entrer votre clé API OpenRouter');
      return;
    }

    setError('');
    setIsLoading(true);

    try {
      const success = await premiumAuthService.authenticateUser(apiKey.trim());
      
      if (success) {
        console.log('🌟 Authentification Premium réussie !');
        setApiKey(''); // Effacer le champ
        // Le modal se fermera automatiquement via l'effet useEffect
      } else {
        // L'erreur sera gérée par le service et transmise via onAuthChange
        console.error('❌ Échec de l\'authentification Premium');
      }
    } catch (err) {
      console.error('❌ Erreur lors de l\'authentification:', err);
      setError('Erreur de connexion. Veuillez réessayer.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setApiKey('');
    setError('');
    setIsLoading(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-slate-800/95 border border-slate-600 rounded-2xl p-6 max-w-lg w-full mx-4 shadow-2xl">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-2xl font-bold text-white flex items-center gap-2">
              🌟 Mode Premium
            </h2>
            <p className="text-slate-300 text-sm mt-1">
              Connectez-vous avec votre clé API OpenRouter
            </p>
          </div>
          <button
            onClick={handleClose}
            className="text-slate-400 hover:text-white transition-colors text-xl"
            disabled={isLoading}
          >
            ✕
          </button>
        </div>

        {/* Formulaire */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="apiKey" className="block text-sm font-medium text-slate-300 mb-2">
              Clé API OpenRouter:
            </label>
            <div className="relative">
              <input
                id="apiKey"
                type={showApiKey ? 'text' : 'password'}
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="sk-or-v1-..."
                className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent pr-12"
                disabled={isLoading}
                autoComplete="off"
              />
              <button
                type="button"
                onClick={() => setShowApiKey(!showApiKey)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white transition-colors"
                disabled={isLoading}
              >
                {showApiKey ? '👁️‍🗨️' : '👁️'}
              </button>
            </div>
          </div>

          {/* Message d'erreur */}
          {error && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
              <p className="text-red-400 text-sm flex items-center gap-2">
                ❌ {error}
              </p>
            </div>
          )}

          {/* Bouton de connexion */}
          <button
            type="submit"
            disabled={isLoading || !apiKey.trim()}
            className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 disabled:from-slate-600 disabled:to-slate-600 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <span className="flex items-center justify-center gap-2">
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                Connexion en cours...
              </span>
            ) : (
              'Se connecter'
            )}
          </button>
        </form>

        {/* Informations supplémentaires */}
        <div className="mt-6 pt-6 border-t border-slate-600">
          <div className="space-y-3">
            <div>
              <h4 className="text-sm font-medium text-slate-300 mb-2">🎯 Avantages Premium:</h4>
              <ul className="text-xs text-slate-400 space-y-1">
                <li>✨ Modèles IA plus puissants (GPT-4o-mini, Claude 3.5 Haiku)</li>
                <li>💰 Prix attractifs (0.1$ à 1$ par million de tokens)</li>
                <li>🔐 Vos propres crédits OpenRouter</li>
                <li>🚀 Performances optimisées</li>
              </ul>
            </div>
            
            <div className="text-center">
              <p className="text-xs text-slate-400 mb-2">
                Pas encore de compte OpenRouter ?
              </p>
              <a
                href="https://openrouter.ai/signup"
                target="_blank"
                rel="noopener noreferrer"
                className="text-purple-400 hover:text-purple-300 text-xs underline transition-colors"
              >
                Créer un compte OpenRouter →
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimplePremiumModal;
